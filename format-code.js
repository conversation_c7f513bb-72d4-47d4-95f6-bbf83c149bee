#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

/**
 * 代码格式化脚本
 *
 * 功能：
 * 1. 保留 ==UserScript== 部分不变
 * 2. 删除注释和空行
 * 3. 数组内对象每个一行，如：
 *    { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
 * 4. 数组内字符串统一放在一行（每5个一行），如：
 *    '長い間（ながいあいだ）', '座り心地（すわりごこち）', '触り心地（さわりごこち）', '申し訳（もうしわけ）', '出張（しゅっちょう）',
 * 5. 方法内的对象尽可能在一行
 * 6. 避免双逗号问题
 *
 * 使用方法：
 *   node format-code.js <输入文件> [输出文件]
 *
 * 示例：
 *   node format-code.js edewakaru.js                    # 输出到 edewakaru.min.js
 *   node format-code.js edewakaru.js formatted.js      # 输出到 formatted.js
 */

function formatCode(inputFile, outputFile) {
  try {
    const content = fs.readFileSync(inputFile, 'utf8')
    const lines = content.split('\n')

    let result = []
    let inUserScript = false
    let userScriptEnd = false
    let inArray = false
    let arrayDepth = 0
    let stringBuffer = []
    let inMultiLineObject = false
    let objectBuffer = ''
    let bracketDepth = 0
    let currentArrayName = ''

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i]
      const trimmedLine = line.trim()

      // 检测 UserScript 区域
      if (trimmedLine.includes('==UserScript==')) {
        inUserScript = true
        result.push(line)
        continue
      }

      if (inUserScript && trimmedLine.includes('==/UserScript==')) {
        result.push(line)
        userScriptEnd = true
        inUserScript = false
        continue
      }

      // UserScript 区域内容保持不变
      if (inUserScript) {
        result.push(line)
        continue
      }

      // UserScript 结束后开始处理
      if (!userScriptEnd) {
        continue
      }

      // 跳过空行和注释行（但保留代码中的重要结构）
      if (trimmedLine === '' || trimmedLine.startsWith('//') || trimmedLine.startsWith('/*') || (trimmedLine.startsWith('*') && !trimmedLine.includes('*/'))) {
        continue
      }

      // 检测数组开始
      if (trimmedLine.includes('[') && !inArray) {
        inArray = true
        arrayDepth = (line.match(/\[/g) || []).length - (line.match(/\]/g) || []).length
        // 提取数组名称
        const arrayMatch = line.match(/const\s+(\w+)\s*=\s*\[/)
        if (arrayMatch) {
          currentArrayName = arrayMatch[1]
        }
        result.push(line)
        continue
      }

      // 在数组内部
      if (inArray) {
        // 更新数组深度
        arrayDepth += (line.match(/\[/g) || []).length - (line.match(/\]/g) || []).length

        // 数组结束
        if (arrayDepth <= 0) {
          // 处理缓存的字符串
          if (stringBuffer.length > 0) {
            const indent = '    '
            result.push(indent + stringBuffer.join(', ') + ',')
            stringBuffer = []
          }

          inArray = false
          result.push(line)
          continue
        }

        // 处理数组内容
        if (trimmedLine.startsWith('{')) {
          // 对象处理
          if (trimmedLine.includes('}')) {
            // 单行对象，直接添加
            result.push('    ' + trimmedLine)
          } else {
            // 多行对象开始
            inMultiLineObject = true
            objectBuffer = trimmedLine
            bracketDepth = 1
          }
        } else if (inMultiLineObject) {
          // 多行对象继续
          bracketDepth += (trimmedLine.match(/\{/g) || []).length
          bracketDepth -= (trimmedLine.match(/\}/g) || []).length

          objectBuffer += ' ' + trimmedLine

          if (bracketDepth <= 0) {
            // 多行对象结束，压缩为一行
            result.push('    ' + objectBuffer)
            inMultiLineObject = false
            objectBuffer = ''
          }
        } else if (trimmedLine.startsWith("'") || trimmedLine.startsWith('"')) {
          // 字符串处理 - 移除末尾逗号后再添加
          let cleanString = trimmedLine.replace(/,$/, '')
          stringBuffer.push(cleanString)

          // 对于RAW_COMPOUND_WORDS数组，每5个字符串输出一行
          // 对于其他数组，保持原有逻辑
          if (currentArrayName === 'RAW_COMPOUND_WORDS' && stringBuffer.length >= 5) {
            const indent = '    '
            result.push(indent + stringBuffer.join(', ') + ',')
            stringBuffer = []
          } else if (currentArrayName !== 'RAW_COMPOUND_WORDS' && stringBuffer.length >= 3) {
            const indent = '    '
            result.push(indent + stringBuffer.join(', ') + ',')
            stringBuffer = []
          }
        } else if (trimmedLine !== '' && !trimmedLine.startsWith('//')) {
          // 其他非空非注释行
          result.push(line)
        }
      } else {
        // 不在数组内，正常处理非注释非空行
        if (trimmedLine !== '' && !trimmedLine.startsWith('//')) {
          result.push(line)
        }
      }
    }

    // 处理剩余的字符串缓存
    if (stringBuffer.length > 0) {
      const indent = '    '
      result.push(indent + stringBuffer.join(', ') + ',')
    }

    // 写入输出文件
    const formattedContent = result.join('\n')
    fs.writeFileSync(outputFile, formattedContent, 'utf8')

    console.log(`✅ 代码格式化完成！`)
    console.log(`📁 输入文件: ${inputFile}`)
    console.log(`📁 输出文件: ${outputFile}`)
    console.log(`📊 原始行数: ${lines.length}`)
    console.log(`📊 格式化后行数: ${result.length}`)
    console.log(`📉 压缩率: ${((1 - result.length / lines.length) * 100).toFixed(1)}%`)
  } catch (error) {
    console.error('❌ 格式化失败:', error.message)
    process.exit(1)
  }
}

// 命令行参数处理
const args = process.argv.slice(2)

if (args.length < 1) {
  console.log('使用方法:')
  console.log('  node format-code.js <输入文件> [输出文件]')
  console.log('')
  console.log('示例:')
  console.log('  node format-code.js edewakaru.js')
  console.log('  node format-code.js edewakaru.js edewakaru.min.js')
  process.exit(1)
}

const inputFile = args[0]
const outputFile = args[1] || inputFile.replace('.js', '.min.js')

// 检查输入文件是否存在
if (!fs.existsSync(inputFile)) {
  console.error(`❌ 输入文件不存在: ${inputFile}`)
  process.exit(1)
}

// 执行格式化
formatCode(inputFile, outputFile)
